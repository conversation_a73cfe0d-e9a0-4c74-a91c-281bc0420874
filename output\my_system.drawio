<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-08-15T06:50:47.000Z" agent="MCP Draw Architecture Server" version="24.7.17">
  <diagram name="电商系统完整技术架构图" id="c0690cd1-61c3-4f36-9cb7-06542db3067d">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <mxCell id="title" value="电商系统完整技术架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="30" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="layer-frontend-title" value="前端层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="100" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-frontend-0" value="**前端层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="325" y="140" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-frontend-1" value="Web前端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="525" y="140" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-frontend-2" value="移动App前端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="725" y="140" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="layer-gateway-title" value="接入层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="220" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-gateway-0" value="API网关" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="525" y="260" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-frontend-0-to-comp-gateway-0" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-frontend-0" target="comp-gateway-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-frontend-1-to-comp-gateway-0" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-frontend-1" target="comp-gateway-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="layer-services-title" value="业务服务层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="340" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-services-0" value="**微服务层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="25" y="380" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-services-1" value="用户服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="225" y="380" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-services-2" value="商品服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="425" y="380" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-services-3" value="订单服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="625" y="380" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-services-4" value="支付服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="825" y="380" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-services-5" value="通知服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1025" y="380" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-gateway-0-to-comp-services-0" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-gateway-0" target="comp-services-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-gateway-0-to-comp-services-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-gateway-0" target="comp-services-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="layer-cache-title" value="缓存层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="460" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-cache-0" value="Redis缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="525" y="500" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-services-0-to-comp-cache-0" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-services-0" target="comp-cache-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-services-1-to-comp-cache-0" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-services-1" target="comp-cache-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="layer-queue-title" value="消息队列层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="580" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-queue-0" value="消息队列（RabbitMQ/Kafka）" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="525" y="620" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-cache-0-to-comp-queue-0" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-cache-0" target="comp-queue-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="layer-database-title" value="数据存储层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="700" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-database-0" value="MySQL主数据库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="225" y="740" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-database-1" value="Elasticsearch" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="425" y="740" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-database-2" value="**数据存储层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="625" y="740" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comp-database-3" value="文件存储（OSS）" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="825" y="740" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-queue-0-to-comp-database-0" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-queue-0" target="comp-database-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-queue-0-to-comp-database-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-queue-0" target="comp-database-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="layer-monitoring-title" value="监控运维层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="820" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comp-monitoring-0" value="监控日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#666666;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="525" y="860" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-database-0-to-comp-monitoring-0" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-database-0" target="comp-monitoring-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-comp-database-1-to-comp-monitoring-0" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="comp-database-1" target="comp-monitoring-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>